# Kubernetes资源配置与调度分析报告

## 问题背景

- **集群配置**: 10个Worker节点，每个节点64GB内存
- **应用类型**: Java服务
- **当前配置**: 
  - Limit: 6GB
  - Request: 500MB 或 1GB (两种方案对比)
- **问题现象**: 某个节点内存使用率远高于其他节点

## 资源配置差异分析

### 方案一: Request 500MB, Limit 6GB

```yaml
resources:
  requests:
    memory: "500Mi"
  limits:
    memory: "6Gi"
```

**特点:**
- **调度密度高**: Kubernetes调度器基于Request值进行调度，500MB的Request意味着理论上单节点可调度128个Pod (64GB/500MB)
- **资源利用率**: 实际内存使用可能远超Request值，导致节点间负载不均
- **风险**: 容易出现资源争抢和OOM

### 方案二: Request 1GB, Limit 6GB

```yaml
resources:
  requests:
    memory: "1Gi"
  limits:
    memory: "6Gi"
```

**特点:**
- **调度密度适中**: 单节点理论可调度64个Pod (64GB/1GB)
- **资源预留**: 为每个Pod预留更多内存，减少资源争抢
- **稳定性**: 更好的性能预期和稳定性

## 调度机制分析

### Kubernetes调度原理

1. **调度阶段**: 调度器基于`requests`值计算节点可用资源
2. **运行阶段**: Pod实际可使用到`limits`值的资源
3. **资源回收**: 当节点资源紧张时，优先回收超出`requests`的Pod

### 节点负载不均的根本原因

```
节点A: 20个Pod × 平均2GB实际使用 = 40GB使用率 (62.5%)
节点B: 25个Pod × 平均1.5GB实际使用 = 37.5GB使用率 (58.6%)
节点C: 30个Pod × 平均2.5GB实际使用 = 75GB使用率 (117% - 触发OOM)
```

**原因分析:**
1. **Request值过小**: 500MB Request无法反映Java应用真实内存需求
2. **调度算法局限**: 默认调度器只考虑Request值，不考虑历史使用情况
3. **应用特性**: Java应用启动后内存使用量通常远超初始分配

## 解决方案建议

### 1. 调整Resource Request (推荐)

**建议配置:**
```yaml
resources:
  requests:
    memory: "2Gi"  # 根据实际使用情况调整
    cpu: "500m"
  limits:
    memory: "6Gi"
    cpu: "2000m"
```

**优势:**
- 更准确反映应用实际需求
- 减少节点间负载差异
- 提高调度质量

### 2. 启用资源监控和自动调整

```yaml
# 使用VPA (Vertical Pod Autoscaler)
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: java-app-vpa
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: java-app
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: java-app
      maxAllowed:
        memory: "6Gi"
      minAllowed:
        memory: "1Gi"
```

### 3. 使用节点亲和性和反亲和性

```yaml
spec:
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: app
              operator: In
              values:
              - java-app
          topologyKey: kubernetes.io/hostname
```

## 监控和优化策略

### 1. 关键指标监控

```bash
# 节点内存使用率
kubectl top nodes

# Pod实际资源使用
kubectl top pods --all-namespaces

# 资源请求vs实际使用对比
kubectl describe nodes | grep -A 5 "Allocated resources"
```

### 2. Java应用优化

```yaml
env:
- name: JAVA_OPTS
  value: "-Xms2g -Xmx5g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

**参数说明:**
- `-Xms2g`: 初始堆内存2GB，与Request匹配
- `-Xmx5g`: 最大堆内存5GB，留1GB给非堆内存
- `UseG1GC`: 使用G1垃圾收集器，适合大内存应用

## 预期效果

### Request 500MB → 2GB 的改进效果

| 指标 | 改进前 | 改进后 | 提升 |
|------|--------|--------|------|
| 节点调度密度 | 128 Pod/节点 | 32 Pod/节点 | 调度更均匀 |
| 内存使用方差 | 高 | 低 | 负载更均衡 |
| OOM风险 | 高 | 低 | 稳定性提升 |
| 资源利用率 | 不可预测 | 可预测 | 容量规划改善 |

## 实施建议

### 阶段一: 评估当前状态
1. 收集所有Java应用的实际内存使用数据
2. 分析节点间负载分布情况
3. 确定合适的Request值范围

### 阶段二: 渐进式调整
1. 选择部分应用先调整Request为1.5-2GB
2. 观察调度效果和稳定性
3. 根据效果调整其他应用

### 阶段三: 监控和优化
1. 建立资源使用监控dashboard
2. 设置告警阈值
3. 定期review和调整资源配置

## 实用脚本

### 节点负载检查脚本

```bash
#!/bin/bash
# check-node-balance.sh - 检查节点间负载均衡情况

echo "=== 节点资源使用情况 ==="
kubectl top nodes --sort-by=memory

echo -e "\n=== 各节点Pod分布 ==="
for node in $(kubectl get nodes -o name | cut -d/ -f2); do
    pod_count=$(kubectl get pods --all-namespaces --field-selector spec.nodeName=$node --no-headers | wc -l)
    echo "节点 $node: $pod_count 个Pod"
done

echo -e "\n=== 内存Request vs 实际使用对比 ==="
kubectl get pods --all-namespaces -o custom-columns=\
"NAMESPACE:.metadata.namespace,NAME:.metadata.name,NODE:.spec.nodeName,\
MEMORY_REQUEST:.spec.containers[*].resources.requests.memory,\
MEMORY_LIMIT:.spec.containers[*].resources.limits.memory" \
| grep -v "<none>"
```

### 资源配置模板

```yaml
# java-app-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: java-app
  labels:
    app: java-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: java-app
  template:
    metadata:
      labels:
        app: java-app
    spec:
      containers:
      - name: java-app
        image: your-java-app:latest
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "6Gi"
            cpu: "2000m"
        env:
        - name: JAVA_OPTS
          value: "-Xms2g -Xmx5g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+PrintGCDetails"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - java-app
              topologyKey: kubernetes.io/hostname
```

## 监控Dashboard配置

### Prometheus查询示例

```promql
# 节点内存使用率
(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100

# Pod内存使用vs Request比率
container_memory_usage_bytes / on(pod) kube_pod_container_resource_requests{resource="memory"}

# 节点间Pod分布标准差
stddev by (cluster) (count by (node, cluster) (kube_pod_info))
```

## 结论

通过将Request从500MB调整到1-2GB，可以显著改善节点间负载不均的问题。关键在于让Request值更接近应用的实际内存需求，从而让Kubernetes调度器做出更准确的调度决策。

**核心建议**: 建议采用Request 2GB, Limit 6GB的配置，这样既能保证调度的准确性，又能为应用提供足够的资源缓冲。

### 立即行动项
1. 使用提供的脚本检查当前节点负载情况
2. 逐步将Java应用的memory request调整到1.5-2GB
3. 监控调整后的效果，特别关注节点间负载均衡情况
4. 根据实际效果进一步微调配置
